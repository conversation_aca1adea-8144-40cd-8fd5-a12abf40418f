<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗄️ قاعدة البيانات - نظام تكامل سلة</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .database-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid var(--primary-color);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: var(--text-muted);
            font-size: 0.9em;
        }
        
        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .table-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .table-title {
            font-size: 1.3em;
            font-weight: bold;
            color: var(--text-dark);
        }
        
        .table-count {
            background: var(--primary-color);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            margin-right: 10px;
        }
        
        .table-description {
            color: var(--text-muted);
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        .table-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-small {
            padding: 8px 15px;
            font-size: 0.9em;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-view {
            background: #007bff;
            color: white;
        }
        
        .btn-view:hover {
            background: #0056b3;
        }
        
        .btn-export {
            background: #28a745;
            color: white;
        }
        
        .btn-export:hover {
            background: #1e7e34;
        }
        
        .btn-clear {
            background: #dc3545;
            color: white;
        }
        
        .btn-clear:hover {
            background: #c82333;
        }
        
        .data-viewer {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }
        
        .record-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }
        
        .record-field {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .field-label {
            font-weight: bold;
            color: var(--text-dark);
        }
        
        .field-value {
            color: var(--text-muted);
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: var(--text-muted);
        }
        
        .empty-state {
            text-align: center;
            padding: 30px;
            color: var(--text-muted);
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            background: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="database-container">
        <div class="page-header">
            <h1><i class="fas fa-database"></i> قاعدة البيانات</h1>
            <p>عرض وإدارة بيانات النظام</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid" id="stats-grid">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-store"></i></div>
                <div class="stat-number" id="stores-count">-</div>
                <div class="stat-label">المتاجر النشطة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-comments"></i></div>
                <div class="stat-number" id="conversations-count">-</div>
                <div class="stat-label">المحادثات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-envelope"></i></div>
                <div class="stat-number" id="messages-count">-</div>
                <div class="stat-label">الرسائل</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-box"></i></div>
                <div class="stat-number" id="products-count">-</div>
                <div class="stat-label">المنتجات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-link"></i></div>
                <div class="stat-number" id="webhooks-count">-</div>
                <div class="stat-label">Webhooks</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-users"></i></div>
                <div class="stat-number" id="users-count">-</div>
                <div class="stat-label">المستخدمين</div>
            </div>
        </div>

        <!-- جداول قاعدة البيانات -->
        <div class="tables-grid">
            <!-- جدول المتاجر -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="stores-table-count">0</span>
                        <span class="table-title">المتاجر</span>
                    </div>
                </div>
                <div class="table-description">
                    معلومات المتاجر المربوطة بالنظام ورموز الوصول الخاصة بها
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('stores')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('stores')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="stores-viewer"></div>
            </div>

            <!-- جدول المحادثات -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="conversations-table-count">0</span>
                        <span class="table-title">المحادثات</span>
                    </div>
                </div>
                <div class="table-description">
                    جلسات الدردشة مع العملاء ومعلومات الاتصال
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('conversations')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('conversations')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="conversations-viewer"></div>
            </div>

            <!-- جدول الرسائل -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="messages-table-count">0</span>
                        <span class="table-title">الرسائل</span>
                    </div>
                </div>
                <div class="table-description">
                    جميع الرسائل المتبادلة بين العملاء والروبوت
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('messages')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('messages')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="messages-viewer"></div>
            </div>

            <!-- جدول المنتجات -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="products-table-count">0</span>
                        <span class="table-title">المنتجات</span>
                    </div>
                </div>
                <div class="table-description">
                    بيانات المنتجات المخزنة مؤقتاً من متاجر سلة
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('products_cache')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('products_cache')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="products_cache-viewer"></div>
            </div>

            <!-- جدول الإعدادات -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="settings-table-count">0</span>
                        <span class="table-title">إعدادات الروبوت</span>
                    </div>
                </div>
                <div class="table-description">
                    إعدادات وتخصيصات الروبوت لكل متجر
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('bot_settings')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('bot_settings')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="bot_settings-viewer"></div>
            </div>

            <!-- جدول التحليلات -->
            <div class="table-card">
                <div class="table-header">
                    <div>
                        <span class="table-count" id="analytics-table-count">0</span>
                        <span class="table-title">التحليلات</span>
                    </div>
                </div>
                <div class="table-description">
                    بيانات التحليلات والإحصائيات المفصلة
                </div>
                <div class="table-actions">
                    <button class="btn-small btn-view" onclick="viewTableData('analytics')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn-small btn-export" onclick="exportTable('analytics')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
                <div class="data-viewer" id="analytics-viewer"></div>
            </div>
        </div>
    </div>

    <!-- زر التحديث -->
    <button class="refresh-btn" onclick="loadDatabaseStats()" title="تحديث البيانات">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // تحميل إحصائيات قاعدة البيانات
        async function loadDatabaseStats() {
            try {
                const response = await fetch('/api/admin/database-stats');
                const data = await response.json();
                
                if (data.success) {
                    updateStats(data.data);
                }
            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }
        
        // تحديث الإحصائيات في الواجهة
        function updateStats(stats) {
            document.getElementById('stores-count').textContent = stats.stores || 0;
            document.getElementById('conversations-count').textContent = stats.conversations || 0;
            document.getElementById('messages-count').textContent = stats.messages || 0;
            document.getElementById('products-count').textContent = stats.products || 0;
            document.getElementById('webhooks-count').textContent = stats.webhooks || 0;
            document.getElementById('users-count').textContent = stats.users || 0;
            
            // تحديث عدادات الجداول
            document.getElementById('stores-table-count').textContent = stats.stores || 0;
            document.getElementById('conversations-table-count').textContent = stats.conversations || 0;
            document.getElementById('messages-table-count').textContent = stats.messages || 0;
            document.getElementById('products-table-count').textContent = stats.products || 0;
            document.getElementById('settings-table-count').textContent = stats.settings || 0;
            document.getElementById('analytics-table-count').textContent = stats.analytics || 0;
        }
        
        // عرض بيانات جدول
        async function viewTableData(tableName) {
            const viewer = document.getElementById(tableName + '-viewer');
            const isVisible = viewer.style.display === 'block';
            
            if (isVisible) {
                viewer.style.display = 'none';
                return;
            }
            
            viewer.style.display = 'block';
            viewer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
            
            try {
                const response = await fetch(`/api/admin/table-data/${tableName}?limit=5`);
                const data = await response.json();
                
                if (data.success && data.data.length > 0) {
                    displayTableData(viewer, data.data, tableName);
                } else {
                    viewer.innerHTML = '<div class="empty-state"><i class="fas fa-inbox"></i><br>لا توجد بيانات</div>';
                }
            } catch (error) {
                viewer.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><br>خطأ في التحميل</div>';
            }
        }
        
        // عرض بيانات الجدول
        function displayTableData(container, records, tableName) {
            let html = '';
            
            records.forEach((record, index) => {
                html += `<div class="record-item">`;
                html += `<div style="font-weight: bold; margin-bottom: 10px;">السجل ${index + 1}</div>`;
                
                // عرض الحقول المهمة حسب نوع الجدول
                const fields = getImportantFields(record, tableName);
                fields.forEach(field => {
                    html += `
                        <div class="record-field">
                            <span class="field-label">${field.label}:</span>
                            <span class="field-value">${field.value}</span>
                        </div>
                    `;
                });
                
                html += `</div>`;
            });
            
            container.innerHTML = html;
        }
        
        // الحصول على الحقول المهمة لكل جدول
        function getImportantFields(record, tableName) {
            const fields = [];
            
            switch (tableName) {
                case 'stores':
                    fields.push(
                        { label: 'اسم المتجر', value: record.store_name || 'غير محدد' },
                        { label: 'رابط المتجر', value: record.store_url || 'غير محدد' },
                        { label: 'معرف المتجر', value: record.store_id || 'غير محدد' },
                        { label: 'نشط', value: record.is_active ? 'نعم' : 'لا' }
                    );
                    break;
                    
                case 'conversations':
                    fields.push(
                        { label: 'معرف المتجر', value: record.store_id || 'غير محدد' },
                        { label: 'اسم العميل', value: record.customer_name || 'غير محدد' },
                        { label: 'معرف الجلسة', value: record.session_id || 'غير محدد' },
                        { label: 'الحالة', value: record.status || 'غير محدد' }
                    );
                    break;
                    
                case 'messages':
                    fields.push(
                        { label: 'معرف المحادثة', value: record.conversation_id || 'غير محدد' },
                        { label: 'نوع المرسل', value: record.sender_type || 'غير محدد' },
                        { label: 'الرسالة', value: (record.message_text || '').substring(0, 50) + '...' },
                        { label: 'نوع الرسالة', value: record.message_type || 'غير محدد' }
                    );
                    break;
                    
                default:
                    // عرض أول 3 حقول
                    const keys = Object.keys(record).slice(0, 3);
                    keys.forEach(key => {
                        fields.push({ label: key, value: record[key] || 'غير محدد' });
                    });
            }
            
            // إضافة تاريخ الإنشاء
            if (record.created_at) {
                fields.push({ label: 'تاريخ الإنشاء', value: new Date(record.created_at).toLocaleString('ar-SA') });
            }
            
            return fields;
        }
        
        // تصدير جدول
        async function exportTable(tableName) {
            try {
                const response = await fetch(`/api/admin/export/${tableName}`);
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${tableName}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                alert('تم تصدير البيانات بنجاح!');
            } catch (error) {
                alert('خطأ في تصدير البيانات');
            }
        }
        
        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            loadDatabaseStats();
            
            // تحديث تلقائي كل 30 ثانية
            setInterval(loadDatabaseStats, 30000);
        });
    </script>
</body>
</html>
