const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');
require('dotenv').config();

// استيراد الوحدات المحلية
const logger = require('./config/logger');
const database = require('./config/database');
const rateLimiter = require('./middleware/rateLimiter');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const sallaRoutes = require('./routes/salla');
const chatbotRoutes = require('./routes/chatbot');
const adminRoutes = require('./routes/admin');
const webhookRoutes = require('./routes/webhooks');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// إعدادات الأمان
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"]
    }
  }
}));

app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourdomain.com']
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

// إعدادات التطبيق
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, '../frontend')));

// تطبيق محدد المعدل
app.use(rateLimiter.generalLimiter);

// تسجيل الطلبات
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// المسارات الرئيسية
app.use('/api/auth', authRoutes);
app.use('/api/salla', sallaRoutes);
app.use('/api/chatbot', chatbotRoutes);
app.use('/api/admin', adminRoutes);
app.use('/webhooks', webhookRoutes);

// صفحة الإدارة
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/admin/index.html'));
});

// صفحة إعداد Webhook
app.get('/admin/webhook-setup', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/admin/webhook-setup.html'));
});

// صفحة قاعدة البيانات
app.get('/admin/database', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/admin/database.html'));
});

// أداة الدردشة
app.get('/widget', (req, res) => {
  res.sendFile(path.join(__dirname, '../frontend/widget/index.html'));
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في نظام تكامل الذكاء الاصطناعي مع سلة',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      admin: '/admin',
      widget: '/widget',
      api: '/api',
      webhooks: '/webhooks'
    }
  });
});

// معالجة الأخطاء 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'الصفحة غير موجودة',
    message: 'المسار المطلوب غير متاح'
  });
});

// معالجة الأخطاء العامة
app.use((error, req, res, next) => {
  logger.error('خطأ في الخادم:', error);
  res.status(500).json({
    error: 'خطأ داخلي في الخادم',
    message: process.env.NODE_ENV === 'development' ? error.message : 'حدث خطأ غير متوقع'
  });
});

// إعداد Socket.IO للدردشة المباشرة
io.on('connection', (socket) => {
  logger.info(`عميل جديد متصل: ${socket.id}`);

  socket.on('join_store', (storeId) => {
    socket.join(`store_${storeId}`);
    logger.info(`العميل ${socket.id} انضم للمتجر ${storeId}`);
  });

  socket.on('chat_message', async (data) => {
    try {
      // معالجة رسالة الدردشة
      const response = await processChatMessage(data);
      socket.emit('bot_response', response);
    } catch (error) {
      logger.error('خطأ في معالجة رسالة الدردشة:', error);
      socket.emit('error', { message: 'حدث خطأ في معالجة رسالتك' });
    }
  });

  socket.on('disconnect', () => {
    logger.info(`العميل ${socket.id} قطع الاتصال`);
  });
});

// دالة معالجة رسائل الدردشة
async function processChatMessage(data) {
  // سيتم تطوير هذه الدالة لاحقاً
  return {
    message: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
    timestamp: new Date().toISOString()
  };
}

// تهيئة قاعدة البيانات وبدء الخادم
async function startServer() {
  try {
    console.log('🔄 بدء تهيئة النظام...');

    // تهيئة قاعدة البيانات
    await database.initialize();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    logger.info('تم تهيئة قاعدة البيانات بنجاح');

    const PORT = process.env.PORT || 3001;

    // التحقق من توفر المنفذ
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ المنفذ ${PORT} مُستخدم بالفعل`);
        console.log('💡 جرب إيقاف العملية الأخرى أو استخدم منفذ آخر');
        process.exit(1);
      } else {
        console.error('❌ خطأ في الخادم:', error);
        process.exit(1);
      }
    });

    server.listen(PORT, () => {
      console.log('\n🎉 تم تشغيل النظام بنجاح!');
      console.log('🚀 الخادم الأساسي يعمل الآن');
      console.log(`🌐 الرابط الرئيسي: http://localhost:${PORT}`);
      console.log(`👨‍💼 لوحة الإدارة: http://localhost:${PORT}/admin`);
      console.log(`🤖 أداة الدردشة: http://localhost:${PORT}/widget`);
      console.log(`🔗 إعداد Webhook: http://localhost:${PORT}/admin/webhook-setup`);
      console.log('\n✅ الميزات المُفعلة:');
      console.log('  • تكامل سلة متقدم');
      console.log('  • دردشة ذكية مع Socket.IO');
      console.log('  • لوحة إدارة شاملة');
      console.log('  • نظام Webhook للتحديثات المباشرة');
      console.log('  • أمان متقدم ومعالجة أخطاء');
      console.log('\n🎯 اضغط Ctrl+C لإيقاف الخادم');

      logger.info(`🚀 الخادم يعمل على المنفذ ${PORT}`);
      logger.info(`🌐 الرابط: http://localhost:${PORT}`);
      logger.info(`👨‍💼 لوحة الإدارة: http://localhost:${PORT}/admin`);
      logger.info(`🤖 أداة الدردشة: http://localhost:${PORT}/widget`);
    });
  } catch (error) {
    console.error('❌ فشل في بدء الخادم:', error);
    logger.error('فشل في بدء الخادم:', error);

    // تفاصيل إضافية للتشخيص
    if (error.code === 'ENOENT') {
      console.error('💡 تحقق من وجود جميع الملفات المطلوبة');
    } else if (error.code === 'EADDRINUSE') {
      console.error('💡 المنفذ مُستخدم، جرب منفذ آخر');
    } else {
      console.error('💡 تحقق من إعدادات النظام وقاعدة البيانات');
    }

    process.exit(1);
  }
}

// معالجة إيقاف التطبيق بشكل صحيح
process.on('SIGTERM', () => {
  logger.info('تم استلام إشارة SIGTERM، إيقاف الخادم...');
  server.close(() => {
    logger.info('تم إيقاف الخادم بنجاح');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('تم استلام إشارة SIGINT، إيقاف الخادم...');
  server.close(() => {
    logger.info('تم إيقاف الخادم بنجاح');
    process.exit(0);
  });
});

// بدء الخادم
startServer();

module.exports = { app, server, io };
