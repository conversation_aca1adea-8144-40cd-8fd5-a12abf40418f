// ===================================================================
// معالج إعداد Webhook للتحديثات المباشرة من سلة
// ===================================================================

const axios = require('axios');
const readline = require('readline');
require('dotenv').config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// إعدادات سلة
const SALLA_CONFIG = {
  clientId: process.env.SALLA_CLIENT_ID,
  clientSecret: process.env.SALLA_CLIENT_SECRET,
  baseUrl: process.env.SALLA_BASE_URL || 'https://api.salla.dev/admin/v2',
  tokenUrl: process.env.SALLA_TOKEN_URL || 'https://accounts.salla.sa/oauth2/token'
};

console.log('🔗 معالج إعداد Webhook للتحديثات المباشرة');
console.log('==========================================\n');

/**
 * طرح سؤال للمستخدم
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * التحقق من صحة URL
 */
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

/**
 * الحصول على Access Token من سلة
 */
async function getAccessToken() {
  try {
    console.log('🔑 جاري الحصول على رمز الوصول من سلة...');
    
    const response = await axios.post(SALLA_CONFIG.tokenUrl, {
      grant_type: 'client_credentials',
      client_id: SALLA_CONFIG.clientId,
      client_secret: SALLA_CONFIG.clientSecret,
      scope: 'read write'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.access_token) {
      console.log('✅ تم الحصول على رمز الوصول بنجاح');
      return response.data.access_token;
    } else {
      throw new Error('لم يتم الحصول على رمز الوصول');
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على رمز الوصول:', error.response?.data || error.message);
    return null;
  }
}

/**
 * تسجيل Webhook في سلة
 */
async function registerWebhook(accessToken, webhookUrl) {
  try {
    console.log('📝 جاري تسجيل Webhook في سلة...');
    
    const webhookData = {
      name: 'AI Chatbot Integration',
      url: webhookUrl,
      events: [
        'order.created',
        'order.updated', 
        'order.cancelled',
        'product.created',
        'product.updated',
        'product.deleted',
        'customer.created',
        'customer.updated'
      ],
      secret: process.env.WEBHOOK_SECRET || 'default_secret'
    };

    const response = await axios.post(
      `${SALLA_CONFIG.baseUrl}/webhooks`,
      webhookData,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    if (response.data) {
      console.log('✅ تم تسجيل Webhook بنجاح!');
      console.log('📋 تفاصيل Webhook:');
      console.log(`   • ID: ${response.data.id}`);
      console.log(`   • URL: ${response.data.url}`);
      console.log(`   • الأحداث: ${response.data.events.join(', ')}`);
      return response.data;
    }
  } catch (error) {
    console.error('❌ خطأ في تسجيل Webhook:', error.response?.data || error.message);
    return null;
  }
}

/**
 * عرض Webhooks المسجلة
 */
async function listWebhooks(accessToken) {
  try {
    console.log('📋 جاري جلب قائمة Webhooks المسجلة...');
    
    const response = await axios.get(
      `${SALLA_CONFIG.baseUrl}/webhooks`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      }
    );

    if (response.data && response.data.data) {
      console.log(`\n📊 عدد Webhooks المسجلة: ${response.data.data.length}`);
      
      response.data.data.forEach((webhook, index) => {
        console.log(`\n${index + 1}. ${webhook.name}`);
        console.log(`   • ID: ${webhook.id}`);
        console.log(`   • URL: ${webhook.url}`);
        console.log(`   • الحالة: ${webhook.status}`);
        console.log(`   • الأحداث: ${webhook.events.join(', ')}`);
      });
      
      return response.data.data;
    }
  } catch (error) {
    console.error('❌ خطأ في جلب Webhooks:', error.response?.data || error.message);
    return [];
  }
}

/**
 * حذف Webhook
 */
async function deleteWebhook(accessToken, webhookId) {
  try {
    console.log(`🗑️ جاري حذف Webhook ${webhookId}...`);
    
    await axios.delete(
      `${SALLA_CONFIG.baseUrl}/webhooks/${webhookId}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      }
    );

    console.log('✅ تم حذف Webhook بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في حذف Webhook:', error.response?.data || error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    // التحقق من الإعدادات
    if (!SALLA_CONFIG.clientId || !SALLA_CONFIG.clientSecret) {
      console.error('❌ يرجى التأكد من إعداد SALLA_CLIENT_ID و SALLA_CLIENT_SECRET في ملف .env');
      process.exit(1);
    }

    // الحصول على رمز الوصول
    const accessToken = await getAccessToken();
    if (!accessToken) {
      console.error('❌ فشل في الحصول على رمز الوصول. يرجى التحقق من إعدادات سلة.');
      process.exit(1);
    }

    // عرض القائمة الرئيسية
    while (true) {
      console.log('\n🔧 اختر العملية المطلوبة:');
      console.log('1. تسجيل Webhook جديد');
      console.log('2. عرض Webhooks المسجلة');
      console.log('3. حذف Webhook');
      console.log('4. اختبار Webhook');
      console.log('5. خروج');

      const choice = await askQuestion('\nاختر رقم العملية: ');

      switch (choice) {
        case '1':
          await handleRegisterWebhook(accessToken);
          break;
        case '2':
          await listWebhooks(accessToken);
          break;
        case '3':
          await handleDeleteWebhook(accessToken);
          break;
        case '4':
          await handleTestWebhook();
          break;
        case '5':
          console.log('👋 شكراً لاستخدام معالج إعداد Webhook');
          process.exit(0);
        default:
          console.log('❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى');
      }
    }
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  } finally {
    rl.close();
  }
}

/**
 * معالج تسجيل Webhook جديد
 */
async function handleRegisterWebhook(accessToken) {
  console.log('\n📝 تسجيل Webhook جديد');
  console.log('===================');
  
  const webhookUrl = await askQuestion('أدخل URL الخاص بـ Webhook: ');
  
  if (!isValidUrl(webhookUrl)) {
    console.log('❌ URL غير صحيح، يرجى المحاولة مرة أخرى');
    return;
  }

  await registerWebhook(accessToken, webhookUrl);
}

/**
 * معالج حذف Webhook
 */
async function handleDeleteWebhook(accessToken) {
  const webhooks = await listWebhooks(accessToken);
  
  if (webhooks.length === 0) {
    console.log('📭 لا توجد Webhooks مسجلة');
    return;
  }

  const webhookId = await askQuestion('\nأدخل ID الخاص بـ Webhook المراد حذفه: ');
  await deleteWebhook(accessToken, webhookId);
}

/**
 * معالج اختبار Webhook
 */
async function handleTestWebhook() {
  console.log('\n🧪 اختبار Webhook');
  console.log('================');
  console.log('لاختبار Webhook، تأكد من:');
  console.log('1. تشغيل النظام على المنفذ 3001');
  console.log('2. تشغيل ngrok: ngrok http 3001');
  console.log('3. استخدام URL مثل: https://abc123.ngrok.io/webhooks/salla');
  console.log('4. التحقق من logs النظام عند وصول البيانات');
}

// تشغيل البرنامج
if (require.main === module) {
  main();
}

module.exports = {
  getAccessToken,
  registerWebhook,
  listWebhooks,
  deleteWebhook
};
