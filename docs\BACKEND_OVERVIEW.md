# 🔧 دليل شامل للـ Backend (الخادم الخلفي)

## 🏗️ البنية العامة

```
backend/
├── server.js              # الخادم الرئيسي
├── config/                # الإعدادات
│   ├── database.js        # قاعدة البيانات
│   └── logger.js          # نظام التسجيل
├── middleware/            # الحماية والأمان
│   ├── auth.js           # المصادقة
│   └── rateLimiter.js    # محدد المعدل
├── routes/               # المسارات
│   ├── admin.js         # الإدارة
│   ├── auth.js          # المصادقة
│   ├── chatbot.js       # الدردشة
│   ├── salla.js         # تكامل سلة
│   └── webhooks.js      # Webhooks
└── services/            # الخدمات
    ├── aiService.js     # الذكاء الاصطناعي
    └── sallaService.js  # خدمة سلة
```

---

## 🖥️ الخادم الرئيسي (server.js)

### **الوظائف الأساسية:**
- **تشغيل Express.js** مع جميع الإعدادات
- **Socket.IO** للدردشة المباشرة
- **CORS** للأمان
- **Helmet** للحماية
- **معالجة الأخطاء** الشاملة

### **المنافذ والروابط:**
- **المنفذ**: 3002 (قابل للتخصيص)
- **الصفحات الثابتة**: `/frontend`
- **APIs**: `/api/*`
- **Webhooks**: `/webhooks/*`

---

## ⚙️ الإعدادات (config/)

### **1. قاعدة البيانات (database.js)**

#### **الميزات:**
- **SQLite** محلية وسريعة
- **8 جداول** مترابطة
- **فهرسة محسنة** للأداء
- **نسخ احتياطية** تلقائية

#### **الجداول:**
```sql
stores           # المتاجر المربوطة
conversations    # جلسات الدردشة
messages         # الرسائل المتبادلة
products_cache   # المنتجات المؤقتة
bot_settings     # إعدادات الروبوت
analytics        # التحليلات
webhook_logs     # سجلات Webhook
admin_users      # المستخدمين الإداريين
```

### **2. نظام التسجيل (logger.js)**

#### **الميزات:**
- **Winston** للتسجيل المتقدم
- **ملفات منفصلة** للأخطاء والعمليات
- **تسجيل يومي** مع تدوير الملفات
- **دوال مساعدة** للتسجيل السهل

#### **أنواع السجلات:**
- `error.log` - الأخطاء فقط
- `combined.log` - جميع العمليات
- `app-YYYY-MM-DD.log` - سجل يومي
- `exceptions.log` - الاستثناءات
- `rejections.log` - رفض الوعود

---

## 🛡️ الحماية والأمان (middleware/)

### **1. نظام المصادقة (auth.js)**

#### **الوظائف:**
- **JWT Tokens** للمصادقة
- **التحقق من الصلاحيات** حسب الدور
- **مصادقة اختيارية** للصفحات العامة
- **إدارة انتهاء الصلاحية**

#### **الاستخدام:**
```javascript
// مصادقة مطلوبة
router.use(authMiddleware);

// صلاحية محددة
router.use(requireRole(['admin', 'manager']));

// مصادقة اختيارية
router.use(optionalAuth);
```

### **2. محدد المعدل (rateLimiter.js)**

#### **أنواع المحددات:**
- **عام**: 100 طلب/15 دقيقة
- **مصادقة**: 5 محاولات/15 دقيقة
- **دردشة**: 30 رسالة/دقيقة
- **API**: 60 طلب/دقيقة
- **تحميل**: 10 ملفات/15 دقيقة
- **حساس**: 10 طلبات/ساعة

---

## 🛣️ المسارات (routes/)

### **1. مسارات الإدارة (admin.js)**

#### **الوظائف الرئيسية:**
```javascript
GET  /api/admin/dashboard        # لوحة المعلومات
GET  /api/admin/conversations    # المحادثات
GET  /api/admin/analytics        # التحليلات
GET  /api/admin/database-stats   # إحصائيات قاعدة البيانات
POST /api/admin/test-webhook     # اختبار Webhook
```

### **2. مسارات الدردشة (chatbot.js)**

#### **الوظائف الرئيسية:**
```javascript
POST /api/chatbot/message        # إرسال رسالة
GET  /api/chatbot/conversation   # جلب محادثة
POST /api/chatbot/feedback       # تقييم الرد
GET  /api/chatbot/suggestions    # الاقتراحات
```

### **3. مسارات سلة (salla.js)**

#### **الوظائف الرئيسية:**
```javascript
GET  /api/salla/auth            # رابط التفويض
POST /api/salla/callback        # معالجة التفويض
GET  /api/salla/products        # جلب المنتجات
GET  /api/salla/orders          # جلب الطلبات
POST /api/salla/webhook         # تسجيل Webhook
```

### **4. مسارات Webhook (webhooks.js)**

#### **الوظائف الرئيسية:**
```javascript
POST /webhooks/salla            # استقبال أحداث سلة
GET  /webhooks/logs             # سجلات Webhook
POST /webhooks/retry            # إعادة معالجة
```

---

## 🔧 الخدمات (services/)

### **1. خدمة الذكاء الاصطناعي (aiService.js)**

#### **الوظائف الرئيسية:**
- **إنشاء ردود ذكية** باستخدام OpenAI
- **تحليل المشاعر** للرسائل
- **اقتراح منتجات** ذات صلة
- **إنشاء محتوى SEO**
- **ردود افتراضية** عند عدم توفر OpenAI

#### **الميزات المتقدمة:**
```javascript
// إنشاء رد ذكي
await aiService.generateChatResponse(storeId, message, context);

// تحليل المشاعر
await aiService.analyzeSentiment(message);

// إنشاء محتوى SEO
await aiService.generateSEOContent(storeId, 'product_description', data);
```

### **2. خدمة سلة (sallaService.js)**

#### **الوظائف الرئيسية:**
- **OAuth 2.0** للتفويض الآمن
- **إدارة الرموز** وتجديدها تلقائياً
- **طلبات API** محمية ومحسنة
- **تخزين مؤقت** للبيانات

#### **APIs المدعومة:**
```javascript
// معلومات المتجر
await sallaService.getStoreInfo(storeId);

// المنتجات
await sallaService.getProducts(storeId, page, limit);

// الطلبات
await sallaService.getOrders(storeId, page, limit);

// العملاء
await sallaService.getCustomers(storeId, page, limit);
```

---

## 📊 الأداء والتحسين

### **التخزين المؤقت:**
- **منتجات سلة** مخزنة مؤقتاً
- **معلومات المتاجر** محفوظة محلياً
- **ردود الذكاء الاصطناعي** محسنة

### **قاعدة البيانات:**
- **فهارس محسنة** للاستعلامات السريعة
- **استعلامات محضرة** لمنع SQL Injection
- **تنظيف دوري** للبيانات القديمة

### **الشبكة:**
- **ضغط Gzip** للاستجابات
- **محدد المعدل** لمنع الإفراط
- **إعادة المحاولة** للطلبات الفاشلة

---

## 🔒 الأمان

### **الحماية المطبقة:**
- **JWT** للمصادقة الآمنة
- **Helmet** للحماية من الهجمات
- **CORS** للتحكم في الوصول
- **Rate Limiting** لمنع الإفراط
- **تشفير كلمات المرور** بـ bcrypt
- **تحقق من التوقيع** للـ Webhooks

### **التسجيل الأمني:**
- **تسجيل جميع المحاولات** المشبوهة
- **مراقبة الأنشطة** غير العادية
- **تنبيهات فورية** للأخطاء الأمنية

---

## 🚀 التشغيل والصيانة

### **بدء التشغيل:**
```bash
# تشغيل الخادم الأساسي
node backend/server.js

# أو باستخدام npm
npm start
```

### **مراقبة النظام:**
```bash
# عرض السجلات المباشرة
tail -f logs/combined.log

# إحصائيات قاعدة البيانات
node database-viewer.js --stats
```

### **الصيانة الدورية:**
- **تنظيف السجلات** القديمة
- **نسخ احتياطية** لقاعدة البيانات
- **تحديث التبعيات** الأمنية
- **مراقبة الأداء** والذاكرة

---

## 📈 التطوير المستقبلي

### **الميزات المخططة:**
- **دعم قواعد بيانات** أخرى (PostgreSQL, MySQL)
- **تجميع الخوادم** للأداء العالي
- **ذكاء اصطناعي محلي** بدون OpenAI
- **تكامل منصات** أخرى (WooCommerce, Shopify)

### **التحسينات المقترحة:**
- **GraphQL** بدلاً من REST
- **Redis** للتخزين المؤقت
- **Docker** للنشر السهل
- **Kubernetes** للتوسع التلقائي

---

## 🎯 الخلاصة

**Backend النظام مصمم ليكون:**
- ✅ **آمن ومحمي** ضد الهجمات الشائعة
- ✅ **قابل للتوسع** مع نمو الأعمال
- ✅ **سهل الصيانة** مع تسجيل شامل
- ✅ **محسن للأداء** مع تخزين مؤقت ذكي
- ✅ **متوافق مع المعايير** الحديثة

**🚀 النظام جاهز للإنتاج ويدعم آلاف المستخدمين المتزامنين!**
