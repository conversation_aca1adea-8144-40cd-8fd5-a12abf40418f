<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 إعداد Webhook - نظام تكامل سلة</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .webhook-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .step-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid var(--primary-color);
        }
        
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        
        .step-title {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--text-dark);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
        }
        
        .url-display {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 1.1em;
            direction: ltr;
            text-align: left;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-right: 5px solid #f39c12;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-right: 5px solid #28a745;
        }
        
        .btn-copy {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        .btn-copy:hover {
            background: var(--secondary-color);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-pending { background: #ffc107; }
    </style>
</head>
<body>
    <div class="webhook-container">
        <div class="page-header">
            <h1><i class="fas fa-link"></i> إعداد Webhook للتحديثات المباشرة</h1>
            <p>اتبع هذه الخطوات لربط متجرك بالنظام للحصول على تحديثات فورية</p>
        </div>

        <!-- الخطوة 1: التحقق من حالة النظام -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">التحقق من حالة النظام</div>
            </div>
            <div class="step-content">
                <p>تأكد من أن النظام يعمل بشكل صحيح:</p>
                <div id="system-status">
                    <p><i class="fas fa-server"></i> حالة الخادم: <span class="status-indicator status-online"></span> متصل</p>
                    <p><i class="fas fa-database"></i> قاعدة البيانات: <span class="status-indicator status-online"></span> متصلة</p>
                    <p><i class="fas fa-comments"></i> نظام الدردشة: <span class="status-indicator status-online"></span> نشط</p>
                </div>
                <div class="success-box">
                    <i class="fas fa-check-circle"></i> النظام يعمل بشكل طبيعي ومستعد لاستقبال Webhooks
                </div>
            </div>
        </div>

        <!-- الخطوة 2: إعداد ngrok -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">إعداد ngrok للوصول العام</div>
            </div>
            <div class="step-content">
                <p>لجعل النظام متاحاً على الإنترنت للاختبار:</p>
                
                <h4>أ) تحميل ngrok:</h4>
                <div class="code-block">
                    # تحميل من الموقع الرسمي
                    https://ngrok.com/download
                    
                    # أو باستخدام chocolatey
                    choco install ngrok
                </div>
                
                <h4>ب) تشغيل ngrok:</h4>
                <div class="code-block">
                    # في terminal جديد
                    ngrok http 3001
                </div>
                
                <h4>ج) نسخ الرابط العام:</h4>
                <div class="warning-box">
                    <i class="fas fa-exclamation-triangle"></i> 
                    انسخ الرابط الذي يبدأ بـ <code>https://</code> من نافذة ngrok
                </div>
                
                <div class="url-display" id="ngrok-url">
                    مثال: https://abc123.ngrok.io
                    <button class="btn-copy" onclick="copyToClipboard('ngrok-url')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                </div>
            </div>
        </div>

        <!-- الخطوة 3: تكوين Webhook URL -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">تكوين Webhook URL</div>
            </div>
            <div class="step-content">
                <p>أضف <code>/webhooks/salla</code> لرابط ngrok:</p>
                
                <div class="url-display" id="webhook-url">
                    https://abc123.ngrok.io/webhooks/salla
                    <button class="btn-copy" onclick="copyToClipboard('webhook-url')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                </div>
                
                <div class="warning-box">
                    <i class="fas fa-info-circle"></i> 
                    هذا هو الرابط الذي ستستخدمه في إعدادات سلة
                </div>
            </div>
        </div>

        <!-- الخطوة 4: تسجيل Webhook في سلة -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">تسجيل Webhook في سلة</div>
            </div>
            <div class="step-content">
                <h4>الطريقة الأولى: استخدام معالج الإعداد</h4>
                <div class="code-block">
                    node webhook-setup.js
                </div>
                
                <h4>الطريقة الثانية: يدوياً عبر لوحة تحكم سلة</h4>
                <ol>
                    <li>ادخل إلى <a href="https://salla.partners" target="_blank">لوحة تحكم شركاء سلة</a></li>
                    <li>اختر تطبيقك</li>
                    <li>اذهب إلى إعدادات Webhooks</li>
                    <li>أضف Webhook جديد</li>
                    <li>الصق الرابط المنسوخ</li>
                    <li>اختر الأحداث المطلوبة</li>
                </ol>
                
                <h4>الأحداث المدعومة:</h4>
                <ul>
                    <li>✅ order.created - طلب جديد</li>
                    <li>✅ order.updated - تحديث طلب</li>
                    <li>✅ product.created - منتج جديد</li>
                    <li>✅ product.updated - تحديث منتج</li>
                    <li>✅ customer.created - عميل جديد</li>
                </ul>
            </div>
        </div>

        <!-- الخطوة 5: اختبار Webhook -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">5</div>
                <div class="step-title">اختبار Webhook</div>
            </div>
            <div class="step-content">
                <p>للتأكد من عمل Webhook بشكل صحيح:</p>
                
                <ol>
                    <li>قم بإنشاء طلب تجريبي في متجرك</li>
                    <li>راقب logs النظام</li>
                    <li>تحقق من وصول البيانات</li>
                </ol>
                
                <div class="success-box">
                    <i class="fas fa-check-circle"></i> 
                    إذا وصلت البيانات بنجاح، فقد تم إعداد Webhook بشكل صحيح!
                </div>
                
                <button class="btn btn-primary" onclick="testWebhook()">
                    <i class="fas fa-vial"></i> اختبار Webhook
                </button>
            </div>
        </div>

        <!-- مراقبة الحالة -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">📊</div>
                <div class="step-title">مراقبة الحالة</div>
            </div>
            <div class="step-content">
                <div id="webhook-status">
                    <p><i class="fas fa-link"></i> حالة Webhook: <span class="status-indicator status-pending"></span> في انتظار الاختبار</p>
                    <p><i class="fas fa-clock"></i> آخر تحديث: لم يتم بعد</p>
                    <p><i class="fas fa-chart-line"></i> عدد الأحداث المستلمة: 0</p>
                </div>
                
                <button class="btn btn-secondary" onclick="refreshStatus()">
                    <i class="fas fa-sync-alt"></i> تحديث الحالة
                </button>
            </div>
        </div>
    </div>

    <script>
        // نسخ النص إلى الحافظة
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent.trim();
            
            navigator.clipboard.writeText(text).then(() => {
                // إظهار رسالة نجاح
                const btn = element.querySelector('.btn-copy');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                btn.style.background = '#28a745';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '';
                }, 2000);
            });
        }
        
        // اختبار Webhook
        async function testWebhook() {
            try {
                const response = await fetch('/api/admin/test-webhook', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ تم إرسال اختبار Webhook بنجاح!');
                } else {
                    alert('❌ فشل في اختبار Webhook: ' + result.message);
                }
            } catch (error) {
                alert('❌ خطأ في الاتصال: ' + error.message);
            }
        }
        
        // تحديث حالة النظام
        async function refreshStatus() {
            try {
                const response = await fetch('/api/admin/webhook-status');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusDisplay(data.data);
                }
            } catch (error) {
                console.error('خطأ في تحديث الحالة:', error);
            }
        }
        
        // تحديث عرض الحالة
        function updateStatusDisplay(data) {
            const statusElement = document.getElementById('webhook-status');
            statusElement.innerHTML = `
                <p><i class="fas fa-link"></i> حالة Webhook: 
                    <span class="status-indicator ${data.isActive ? 'status-online' : 'status-offline'}"></span> 
                    ${data.isActive ? 'نشط' : 'غير نشط'}
                </p>
                <p><i class="fas fa-clock"></i> آخر تحديث: ${data.lastUpdate || 'لم يتم بعد'}</p>
                <p><i class="fas fa-chart-line"></i> عدد الأحداث المستلمة: ${data.eventsCount || 0}</p>
            `;
        }
        
        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            refreshStatus();
            
            // تحديث تلقائي كل 30 ثانية
            setInterval(refreshStatus, 30000);
        });
    </script>
</body>
</html>
