@echo off
echo 🔗 إعداد Webhook URL للتحديثات المباشرة
echo ==========================================

echo.
echo 📋 الخطوات المطلوبة:
echo 1. تحميل وتثبيت ngrok
echo 2. تشغيل النظام
echo 3. إنشاء tunnel عام
echo 4. تحديث إعدادات سلة

echo.
echo 📥 تحميل ngrok...
echo يرجى تحميل ngrok من: https://ngrok.com/download
echo أو استخدم الأمر التالي إذا كان لديك chocolatey:
echo choco install ngrok

echo.
echo 🚀 بعد تثبيت ngrok، شغل الأوامر التالية:
echo.
echo 1. تشغيل النظام:
echo    npm start
echo.
echo 2. في terminal جديد، شغل ngrok:
echo    ngrok http 3001
echo.
echo 3. انسخ الرابط العام الذي سيظهر
echo    مثال: https://abc123.ngrok.io
echo.
echo 4. أضف /webhooks/salla في النهاية
echo    مثال: https://abc123.ngrok.io/webhooks/salla

echo.
echo 📝 ملاحظات مهمة:
echo - ngrok مجاني للاستخدام الأساسي
echo - الرابط يتغير كل مرة تعيد تشغيل ngrok
echo - للاستخدام الدائم، احتج خادم حقيقي

pause
