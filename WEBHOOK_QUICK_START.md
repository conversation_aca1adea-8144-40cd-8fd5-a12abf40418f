# 🚀 دليل البدء السريع لإعداد Webhook

## 📋 الخطوات السريعة (5 دقائق)

### 1. **تشغيل النظام**
```bash
# تشغيل النظام
npm start
# أو
node server-clean.js
```

### 2. **تحميل وتشغيل ngrok**
```bash
# تحميل ngrok من https://ngrok.com/download
# أو تثبيت باستخدام chocolatey
choco install ngrok

# تشغيل ngrok في terminal جديد
ngrok http 3001
```

### 3. **نسخ رابط Webhook**
من نافذة ngrok، انسخ الرابط الذي يبدأ بـ `https://`
```
مثال: https://abc123.ngrok.io
```

أضف `/webhooks/salla` في النهاية:
```
https://abc123.ngrok.io/webhooks/salla
```

### 4. **تسجيل Webhook في سلة**

#### **الطريقة السهلة: استخدام معالج الإعداد**
```bash
node webhook-setup.js
```

#### **الطريقة اليدوية:**
1. ادخل إلى [لوحة تحكم شركاء سلة](https://salla.partners)
2. اختر تطبيقك
3. اذهب إلى إعدادات Webhooks
4. أضف Webhook جديد
5. الصق الرابط: `https://abc123.ngrok.io/webhooks/salla`
6. اختر الأحداث:
   - ✅ order.created
   - ✅ order.updated
   - ✅ product.created
   - ✅ product.updated
   - ✅ customer.created

### 5. **اختبار Webhook**
- قم بإنشاء طلب تجريبي في متجرك
- راقب logs النظام
- أو استخدم صفحة الاختبار: http://localhost:3001/admin/webhook-setup

---

## 🔧 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `webhook-setup.js` | معالج إعداد Webhook تلقائياً |
| `start-with-ngrok.bat` | تشغيل النظام مع ngrok |
| `docs/WEBHOOK_SETUP.md` | دليل مفصل |

---

## 🌐 الروابط المفيدة

| الصفحة | الرابط |
|---------|---------|
| 🏠 الرئيسية | http://localhost:3001 |
| 👨‍💼 لوحة الإدارة | http://localhost:3001/admin |
| 🔗 إعداد Webhook | http://localhost:3001/admin/webhook-setup |
| 🤖 أداة الدردشة | http://localhost:3001/widget |

---

## ⚠️ نصائح مهمة

### ✅ **للنجاح:**
- تأكد من تشغيل النظام قبل تشغيل ngrok
- استخدم الرابط الكامل مع `/webhooks/salla`
- راقب logs للتأكد من وصول البيانات

### ❌ **تجنب:**
- إغلاق ngrok أثناء الاختبار
- استخدام HTTP بدلاً من HTTPS
- نسيان إضافة `/webhooks/salla`

---

## 🆘 حل المشاكل السريع

### **المشكلة: Webhook لا يصل**
```bash
# تحقق من:
1. هل النظام يعمل على المنفذ 3001؟
2. هل ngrok يعمل ويظهر رابط https؟
3. هل تم تسجيل الرابط الصحيح في سلة؟
```

### **المشكلة: خطأ في الاتصال**
```bash
# تحقق من:
1. إعدادات SALLA_CLIENT_ID و SALLA_CLIENT_SECRET في .env
2. صحة الرابط المستخدم
3. حالة الإنترنت
```

---

## 📞 الدعم السريع

1. **راجع logs النظام** في terminal
2. **تحقق من صفحة الحالة**: http://localhost:3001/admin/webhook-setup
3. **اختبر مع ngrok أولاً** قبل الانتقال للإنتاج
4. **راجع وثائق سلة**: https://docs.salla.dev/

---

## 🎉 بعد النجاح

عند نجاح إعداد Webhook:
- ✅ ستصل إشعارات فورية عند إنشاء طلبات جديدة
- ✅ سيتم تحديث بيانات المنتجات تلقائياً
- ✅ ستحصل على إحصائيات مفصلة في لوحة الإدارة
- ✅ سيعمل الروبوت بكفاءة أعلى مع البيانات المحدثة

**🚀 مبروك! نظامك الآن متصل بالكامل مع سلة!**
