// ===================================================================
// خادم نظام تكامل الذكاء الاصطناعي مع سلة - الإصدار النظيف والمحسن
// ===================================================================

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// تحميل متغيرات البيئة
require('dotenv').config();

// محاولة تحميل Socket.IO
let socketIo = null;
try {
  socketIo = require('socket.io');
  console.log('✅ Socket.IO محمل بنجاح');
} catch (error) {
  console.log('⚠️ Socket.IO غير متوفر - سيتم استخدام HTTP فقط');
}

// ===================================================================
// إعدادات النظام
// ===================================================================

const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

// إعدادات سلة
const SALLA_CONFIG = {
  clientId: process.env.SALLA_CLIENT_ID,
  clientSecret: process.env.SALLA_CLIENT_SECRET,
  baseUrl: process.env.SALLA_BASE_URL || 'https://api.salla.dev/admin/v2',
  oauthUrl: process.env.SALLA_OAUTH_URL || 'https://accounts.salla.sa/oauth2/authorize',
  tokenUrl: process.env.SALLA_TOKEN_URL || 'https://accounts.salla.sa/oauth2/token'
};

// إعدادات OpenAI
const OPENAI_CONFIG = {
  apiKey: process.env.OPENAI_API_KEY,
  model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo'
};

// متغيرات النظام
let systemStats = {
  startTime: new Date(),
  totalRequests: 0,
  activeConnections: 0,
  totalMessages: 0,
  errors: 0
};

// ===================================================================
// دوال مساعدة
// ===================================================================

/**
 * إنشاء المجلدات المطلوبة
 */
function initializeDirectories() {
  const folders = ['database', 'logs', 'uploads'];
  folders.forEach(folder => {
    try {
      if (!fs.existsSync(folder)) {
        fs.mkdirSync(folder, { recursive: true });
        console.log(`✅ تم إنشاء مجلد: ${folder}`);
      }
    } catch (error) {
      console.error(`❌ فشل في إنشاء مجلد ${folder}:`, error.message);
    }
  });
}

/**
 * قراءة ملف مع معالجة الأخطاء
 */
function readFile(filePath, defaultContent = '') {
  try {
    if (fs.existsSync(filePath)) {
      return fs.readFileSync(filePath, 'utf8');
    }
    return defaultContent;
  } catch (error) {
    console.error(`❌ خطأ في قراءة الملف ${filePath}:`, error.message);
    systemStats.errors++;
    return defaultContent;
  }
}

/**
 * تحديد نوع المحتوى للملفات
 */
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css; charset=utf-8',
    '.js': 'application/javascript; charset=utf-8',
    '.json': 'application/json; charset=utf-8',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf'
  };
  return types[ext] || 'text/plain; charset=utf-8';
}

/**
 * خدمة الملفات الثابتة مع تحسينات
 */
function serveStaticFile(req, res, filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);

    if (!fs.existsSync(fullPath)) {
      return false;
    }

    const stats = fs.statSync(fullPath);
    const content = fs.readFileSync(fullPath);
    const contentType = getContentType(fullPath);

    // إضافة headers للتحسين
    res.writeHead(200, {
      'Content-Type': contentType,
      'Content-Length': stats.size,
      'Cache-Control': 'public, max-age=3600',
      'Last-Modified': stats.mtime.toUTCString()
    });

    res.end(content);
    return true;
  } catch (error) {
    console.error(`❌ خطأ في خدمة الملف ${filePath}:`, error.message);
    systemStats.errors++;
    return false;
  }
}

/**
 * تسجيل الطلبات مع الإحصائيات
 */
function logRequest(req) {
  systemStats.totalRequests++;
  if (NODE_ENV === 'development') {
    console.log(`📝 ${req.method} ${req.url}`);
  }
}

/**
 * إرسال استجابة خطأ منسقة
 */
function sendErrorResponse(res, statusCode, message, details = null) {
  const errorResponse = {
    error: true,
    message: message,
    statusCode: statusCode,
    timestamp: new Date().toISOString()
  };

  if (details && NODE_ENV === 'development') {
    errorResponse.details = details;
  }

  res.writeHead(statusCode, { 'Content-Type': 'application/json; charset=utf-8' });
  res.end(JSON.stringify(errorResponse, null, 2));
}

/**
 * إرسال استجابة 404
 */
function send404Response(res) {
  res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
  res.end(`
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
      <meta charset="UTF-8">
      <title>404 - الصفحة غير موجودة</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          text-align: center;
          padding: 100px;
          background: #f8fafc;
        }
        .error-container {
          max-width: 500px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 10px;
          box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 { color: #ef4444; font-size: 3em; margin-bottom: 20px; }
        p { color: #64748b; margin-bottom: 30px; }
        a {
          background: #2563eb;
          color: white;
          padding: 12px 24px;
          text-decoration: none;
          border-radius: 5px;
          display: inline-block;
        }
        a:hover { background: #1d4ed8; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <h1>404</h1>
        <p>الصفحة التي تبحث عنها غير موجودة</p>
        <a href="/">🏠 العودة للرئيسية</a>
      </div>
    </body>
    </html>
  `);
}

// تهيئة المجلدات
initializeDirectories();

// ===================================================================
// معالجات الصفحات
// ===================================================================

/**
 * خدمة الصفحة الرئيسية المحسنة
 */
function serveHomePage(res) {
  try {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤖 نظام تكامل الذكاء الاصطناعي مع سلة - BRQ AI</title>
        <meta name="description" content="نظام متطور لربط روبوتات الدردشة الذكية مع متاجر سلة لتحسين تجربة العملاء وزيادة المبيعات">
        <meta name="keywords" content="ذكاء اصطناعي, سلة, دردشة, روبوت, تجارة إلكترونية">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
          :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #00d4aa;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --text-light: #ffffff;
            --text-dark: #1f2937;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
          }

          /* خلفية متحركة */
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
              radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(0, 212, 170, 0.2) 0%, transparent 50%);
            animation: backgroundMove 20s ease-in-out infinite;
            z-index: -1;
          }

          @keyframes backgroundMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            33% { transform: translateX(-30px) translateY(-30px); }
            66% { transform: translateX(30px) translateY(-30px); }
          }

          .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
          }

          .hero-section {
            text-align: center;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            padding: 60px 40px;
            border-radius: 30px;
            box-shadow:
              0 25px 50px rgba(0, 0, 0, 0.1),
              0 0 0 1px rgba(255, 255, 255, 0.1);
            max-width: 900px;
            width: 100%;
            color: var(--text-light);
            position: relative;
            overflow: hidden;
          }

          .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.03), transparent);
            animation: shimmer 3s ease-in-out infinite;
          }

          @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
          }

          .logo {
            font-size: 4em;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }

          .main-title {
            font-size: 3em;
            font-weight: 900;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
          }

          .subtitle {
            font-size: 1.3em;
            font-weight: 400;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
          }

          .status-card {
            background: linear-gradient(135deg, var(--success-color), #059669);
            border: none;
            color: white;
            padding: 25px;
            border-radius: 20px;
            margin: 40px 0;
            font-weight: 600;
            font-size: 1.2em;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            animation: pulse 2s ease-in-out infinite;
          }

          @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
          }

          .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 50px 0;
          }

          .feature-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            padding: 30px 25px;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
          }

          .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: var(--accent-color);
          }

          .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            background: linear-gradient(45deg, var(--accent-color), #00b894);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .feature-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--text-light);
          }

          .feature-description {
            font-size: 1em;
            opacity: 0.8;
            line-height: 1.5;
          }

          .action-buttons {
            display: flex;
            gap: 25px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 50px;
          }

          .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--accent-color), #00b894);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1em;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            min-width: 200px;
            justify-content: center;
            position: relative;
            overflow: hidden;
          }

          .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          .action-btn:hover::before {
            left: 100%;
          }

          .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 212, 170, 0.4);
            border-color: rgba(255, 255, 255, 0.3);
          }

          .action-btn.secondary {
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            color: var(--text-light);
          }

          .action-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 30px rgba(255, 255, 255, 0.1);
          }

          .stats-section {
            margin-top: 60px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 30px;
          }

          .stat-item {
            text-align: center;
            padding: 20px;
            background: var(--glass-bg);
            border-radius: 15px;
            border: 1px solid var(--glass-border);
          }

          .stat-number {
            font-size: 2.5em;
            font-weight: 900;
            color: var(--accent-color);
            display: block;
          }

          .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
          }

          .footer-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid var(--glass-border);
            font-size: 0.9em;
            opacity: 0.7;
          }

          /* استجابة للشاشات الصغيرة */
          @media (max-width: 768px) {
            .main-title {
              font-size: 2.2em;
            }

            .hero-section {
              padding: 40px 25px;
              margin: 10px;
            }

            .action-buttons {
              flex-direction: column;
              align-items: center;
            }

            .action-btn {
              width: 100%;
              max-width: 300px;
            }
          }

          /* تأثيرات إضافية */
          .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
          }

          @keyframes fadeInUp {
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        </style>
      </head>
      <body>
        <div class="main-container">
          <div class="hero-section">
            <div class="logo">🤖</div>
            <h1 class="main-title">نظام تكامل الذكاء الاصطناعي مع سلة</h1>
            <p class="subtitle">
              منصة متطورة لربط روبوتات الدردشة الذكية مع متاجر سلة<br>
              لتحسين تجربة العملاء وزيادة المبيعات بشكل تلقائي
            </p>

            <div class="status-card">
              <i class="fas fa-check-circle"></i>
              <span>النظام يعمل بنجاح ومتاح للاستخدام</span>
            </div>

            <div class="features-grid">
              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-brain"></i>
                </div>
                <h3 class="feature-title">ذكاء اصطناعي متقدم</h3>
                <p class="feature-description">مدعوم بتقنيات OpenAI GPT لردود ذكية ومفيدة تحاكي التفاعل البشري</p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-store"></i>
                </div>
                <h3 class="feature-title">تكامل سلة المتقدم</h3>
                <p class="feature-description">ربط مباشر وآمن مع متاجر سلة لإدارة المنتجات والطلبات تلقائياً</p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-comments"></i>
                </div>
                <h3 class="feature-title">دردشة مباشرة</h3>
                <p class="feature-description">تفاعل فوري مع العملاء عبر Socket.IO بدون تأخير أو انقطاع</p>
              </div>

              <div class="feature-card">
                <div class="feature-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">تحليلات شاملة</h3>
                <p class="feature-description">إحصائيات مفصلة وتقارير مباشرة لمراقبة الأداء وتحسين النتائج</p>
              </div>
            </div>

            <div class="action-buttons">
              <a href="/admin" class="action-btn">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة الإدارة</span>
              </a>
              <a href="/widget" class="action-btn secondary">
                <i class="fas fa-robot"></i>
                <span>تجربة الدردشة</span>
              </a>
              <a href="/api" class="action-btn secondary">
                <i class="fas fa-code"></i>
                <span>واجهة API</span>
              </a>
            </div>

            <div class="stats-section">
              <div class="stat-item">
                <span class="stat-number" id="uptime">0</span>
                <span class="stat-label">ثانية تشغيل</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="requests">0</span>
                <span class="stat-label">طلب معالج</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="messages">0</span>
                <span class="stat-label">رسالة مرسلة</span>
              </div>
              <div class="stat-item">
                <span class="stat-number" id="connections">0</span>
                <span class="stat-label">اتصال نشط</span>
              </div>
            </div>

            <div class="footer-info">
              <p>
                <i class="fas fa-globe"></i>
                تم تطوير هذا النظام بواسطة <strong>BRQ AI</strong> -
                <a href="https://brqai.com" target="_blank" style="color: var(--accent-color);">brqai.com</a>
              </p>
            </div>
          </div>
        </div>

        <script>
          // تحديث الإحصائيات المباشرة
          async function updateStats() {
            try {
              const response = await fetch('/api');
              const data = await response.json();

              if (data.stats) {
                document.getElementById('uptime').textContent = data.uptime || 0;
                document.getElementById('requests').textContent = data.stats.totalRequests || 0;
                document.getElementById('messages').textContent = data.stats.totalMessages || 0;
                document.getElementById('connections').textContent = data.stats.activeConnections || 0;
              }
            } catch (error) {
              console.log('تعذر تحديث الإحصائيات:', error);
            }
          }

          // تحديث الإحصائيات كل 5 ثوان
          updateStats();
          setInterval(updateStats, 5000);

          // تأثيرات التحميل
          document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.feature-card, .stat-item');
            elements.forEach((el, index) => {
              el.style.animationDelay = (index * 0.1) + 's';
              el.classList.add('animate-on-scroll');
            });
          });

          // تأثير الماوس للبطاقات
          document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mousemove', (e) => {
              const rect = card.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;

              card.style.setProperty('--mouse-x', x + 'px');
              card.style.setProperty('--mouse-y', y + 'px');
            });
          });
        </script>
      </body>
      </html>
    `);
  } catch (error) {
    console.error('❌ خطأ في خدمة الصفحة الرئيسية:', error);
    sendErrorResponse(res, 500, 'خطأ في تحميل الصفحة الرئيسية');
  }
}

/**
 * خدمة لوحة الإدارة
 */
function serveAdminPage(res) {
  try {
    const adminPath = path.join(__dirname, 'frontend', 'admin', 'index.html');
    const content = readFile(adminPath, '<h1>لوحة الإدارة قيد التطوير</h1>');
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(content);
  } catch (error) {
    console.error('❌ خطأ في خدمة لوحة الإدارة:', error);
    sendErrorResponse(res, 500, 'خطأ في تحميل لوحة الإدارة');
  }
}

/**
 * خدمة أداة الدردشة
 */
function serveWidgetPage(res) {
  try {
    const widgetPath = path.join(__dirname, 'frontend', 'widget', 'index.html');
    const content = readFile(widgetPath, '<h1>أداة الدردشة قيد التطوير</h1>');
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(content);
  } catch (error) {
    console.error('❌ خطأ في خدمة أداة الدردشة:', error);
    sendErrorResponse(res, 500, 'خطأ في تحميل أداة الدردشة');
  }
}

// ===================================================================
// معالجات API
// ===================================================================

/**
 * معالجة طلبات API
 */
function handleApiRequest(req, res, pathname, parsedUrl) {
  try {
    // API الرئيسي
    if (pathname === '/api') {
      return handleMainApi(res);
    }

    // تكامل سلة - بدء المصادقة
    if (pathname === '/api/salla/auth') {
      return handleSallaAuth(res);
    }

    // تكامل سلة - معالجة الاستجابة
    if (pathname === '/api/salla/callback') {
      return handleSallaCallback(res, parsedUrl);
    }

    // API غير موجود
    sendErrorResponse(res, 404, 'API غير موجود');
  } catch (error) {
    console.error('❌ خطأ في معالجة API:', error);
    sendErrorResponse(res, 500, 'خطأ في معالجة API');
  }
}

/**
 * معالجة API الرئيسي
 */
function handleMainApi(res) {
  const response = {
    message: 'مرحباً بك في API نظام تكامل الذكاء الاصطناعي مع سلة',
    version: '3.0.0',
    status: 'running',
    uptime: Math.floor((Date.now() - systemStats.startTime) / 1000),
    stats: systemStats,
    salla: {
      configured: !!(SALLA_CONFIG.clientId && SALLA_CONFIG.clientSecret),
      clientId: SALLA_CONFIG.clientId ? SALLA_CONFIG.clientId.substring(0, 8) + '...' : 'غير محدد'
    },
    openai: {
      configured: !!OPENAI_CONFIG.apiKey,
      model: OPENAI_CONFIG.model
    },
    features: [
      'روبوت دردشة ذكي',
      'تكامل مع سلة',
      'لوحة إدارة متقدمة',
      'تحليلات شاملة',
      socketIo ? 'دردشة مباشرة' : null
    ].filter(Boolean),
    endpoints: {
      admin: '/admin',
      widget: '/widget',
      api: '/api',
      sallaAuth: '/api/salla/auth',
      sallaCallback: '/api/salla/callback'
    },
    warnings: [
      !socketIo ? 'Socket.IO غير مثبت - الدردشة المباشرة معطلة' : null,
      !OPENAI_CONFIG.apiKey ? 'OpenAI API غير مُعرف - ردود محلية فقط' : null
    ].filter(Boolean),
    timestamp: new Date().toISOString()
  };

  res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
  res.end(JSON.stringify(response, null, 2));
}

/**
 * معالجة تفويض سلة
 */
function handleSallaAuth(res) {
  if (!SALLA_CONFIG.clientId) {
    return sendErrorResponse(res, 400, 'إعدادات سلة غير مكتملة');
  }

  const authUrl = `${SALLA_CONFIG.oauthUrl}?response_type=code&client_id=${SALLA_CONFIG.clientId}&redirect_uri=${encodeURIComponent('http://localhost:' + PORT + '/api/salla/callback')}&scope=offline_access`;

  res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
  res.end(JSON.stringify({
    authUrl: authUrl,
    message: 'انتقل إلى الرابط لتفويض التطبيق'
  }));
}

/**
 * معالجة استجابة تفويض سلة
 */
function handleSallaCallback(res, parsedUrl) {
  const query = parsedUrl.query;

  if (query.error) {
    return sendErrorResponse(res, 400, 'فشل في التفويض', query.error_description || query.error);
  }

  if (query.code) {
    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
    res.end(JSON.stringify({
      success: true,
      message: 'تم التفويض بنجاح!',
      code: query.code,
      nextStep: 'سيتم الآن تبديل الكود برمز الوصول'
    }));
    return;
  }

  sendErrorResponse(res, 400, 'كود التفويض مفقود');
}

/**
 * معالجة الملفات الثابتة
 */
function handleStaticFiles(req, res, pathname) {
  // محاولة خدمة الملف من مسارات مختلفة
  const possiblePaths = [
    pathname.startsWith('/frontend/') ? pathname : null,
    path.join('frontend', 'admin', path.basename(pathname)),
    path.join('frontend', 'widget', path.basename(pathname))
  ].filter(Boolean);

  for (const filePath of possiblePaths) {
    if (serveStaticFile(req, res, filePath)) {
      return true;
    }
  }

  return false;
}

// ===================================================================
// توجيه الطلبات الرئيسي
// ===================================================================

/**
 * توجيه الطلبات للمعالجات المناسبة
 */
function routeRequest(req, res, pathname, parsedUrl) {
  // الصفحة الرئيسية
  if (pathname === '/') {
    return serveHomePage(res);
  }

  // لوحة الإدارة
  if (pathname === '/admin') {
    return serveAdminPage(res);
  }

  // أداة الدردشة
  if (pathname === '/widget') {
    return serveWidgetPage(res);
  }

  // APIs
  if (pathname.startsWith('/api')) {
    return handleApiRequest(req, res, pathname, parsedUrl);
  }

  // الملفات الثابتة
  if (pathname.startsWith('/frontend/') ||
      pathname.endsWith('.css') ||
      pathname.endsWith('.js') ||
      pathname.endsWith('.ico')) {
    if (handleStaticFiles(req, res, pathname)) {
      return;
    }
  }

  // 404 - الصفحة غير موجودة
  return send404Response(res);
}

// ===================================================================
// إنشاء الخادم الرئيسي
// ===================================================================

const server = http.createServer((req, res) => {
  try {
    // تسجيل الطلب
    logRequest(req);

    // إعداد CORS محسن
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400'
    };

    Object.entries(corsHeaders).forEach(([key, value]) => {
      res.setHeader(key, value);
    });

    // معالجة طلبات OPTIONS
    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;

    // توجيه الطلبات
    routeRequest(req, res, pathname, parsedUrl);

  } catch (error) {
    console.error('❌ خطأ في معالجة الطلب:', error);
    systemStats.errors++;
    sendErrorResponse(res, 500, 'خطأ داخلي في الخادم');
  }
});

// ===================================================================
// إعداد Socket.IO للدردشة المباشرة
// ===================================================================

if (socketIo) {
  const io = socketIo(server, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    }
  });

  // متغيرات للدردشة المباشرة
  let activeConnections = new Map();
  let liveStats = {
    activeUsers: 0,
    totalMessages: 0,
    activeConversations: 0
  };

  // معالجة اتصالات Socket.IO
  io.on('connection', (socket) => {
    console.log(`🔗 عميل جديد متصل: ${socket.id}`);
    liveStats.activeUsers++;
    systemStats.activeConnections++;

    // إرسال الإحصائيات المحدثة
    io.emit('stats_update', liveStats);

    // انضمام لغرفة متجر معين
    socket.on('join_store', (storeId) => {
      socket.join(`store_${storeId}`);
      activeConnections.set(socket.id, { storeId, joinTime: new Date() });
      console.log(`👤 العميل ${socket.id} انضم للمتجر ${storeId}`);
    });

    // معالجة رسائل الدردشة
    socket.on('chat_message', async (data) => {
      try {
        console.log(`💬 رسالة جديدة من ${socket.id}:`, data.message);

        liveStats.totalMessages++;
        systemStats.totalMessages++;

        // توليد رد ذكي
        const botResponse = await generateBotResponse(data.message, data.storeId);

        // إرسال الرد للعميل
        socket.emit('bot_response', {
          message: botResponse,
          timestamp: new Date().toISOString(),
          type: 'text'
        });

        // إرسال تحديث للإدارة
        io.emit('new_message', {
          socketId: socket.id,
          storeId: data.storeId,
          userMessage: data.message,
          botResponse: botResponse,
          timestamp: new Date().toISOString()
        });

        // تحديث الإحصائيات
        io.emit('stats_update', liveStats);

      } catch (error) {
        console.error('❌ خطأ في معالجة الرسالة:', error);
        systemStats.errors++;
        socket.emit('error', {
          message: 'حدث خطأ في معالجة رسالتك، يرجى المحاولة مرة أخرى'
        });
      }
    });

    // قطع الاتصال
    socket.on('disconnect', () => {
      console.log(`🔌 العميل ${socket.id} قطع الاتصال`);
      activeConnections.delete(socket.id);
      liveStats.activeUsers--;
      systemStats.activeConnections--;
      io.emit('stats_update', liveStats);
    });
  });

  /**
   * توليد ردود ذكية (مؤقتة حتى يتم ربط OpenAI)
   */
  async function generateBotResponse(message, storeId) {
    const responses = [
      'مرحباً! كيف يمكنني مساعدتك اليوم؟ 😊',
      'أهلاً وسهلاً! أنا هنا للإجابة على استفساراتك',
      'شكراً لتواصلك معنا! كيف يمكنني خدمتك؟',
      'مرحباً بك في متجرنا! ما الذي تبحث عنه؟',
      'أهلاً! سأكون سعيداً لمساعدتك في العثور على ما تحتاجه'
    ];

    // محاكاة تأخير الرد
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    return responses[Math.floor(Math.random() * responses.length)];
  }

  console.log('✅ Socket.IO مُفعل - الدردشة المباشرة متاحة');
} else {
  console.log('⚠️ Socket.IO غير متوفر - الدردشة المباشرة معطلة');
}

// ===================================================================
// بدء الخادم ومعالجة الأخطاء
// ===================================================================

server.listen(PORT, () => {
  console.log('\n🎉 تم تشغيل النظام بنجاح!');
  console.log('🚀 الخادم النظيف والمحسن يعمل الآن');
  console.log(`🌐 الرابط الرئيسي: http://localhost:${PORT}`);
  console.log(`👨‍💼 لوحة الإدارة: http://localhost:${PORT}/admin`);
  console.log(`🤖 أداة الدردشة: http://localhost:${PORT}/widget`);
  console.log(`📊 واجهة API: http://localhost:${PORT}/api`);

  console.log('\n✅ الميزات المُفعلة:');
  console.log('  • واجهات محسنة ومتجاوبة');
  console.log('  • كود منظف ومحسن');
  console.log('  • معالجة أخطاء شاملة');
  console.log('  • تكامل سلة متقدم');
  console.log('  • ردود ذكية في الدردشة');
  console.log('  • لوحة إدارة كاملة');
  if (socketIo) console.log('  • دردشة مباشرة مع Socket.IO');

  console.log('\n⚠️ تحذيرات:');
  if (!socketIo) console.log('  • Socket.IO غير مثبت - الدردشة المباشرة معطلة');
  if (!OPENAI_CONFIG.apiKey) console.log('  • OpenAI API غير مُعرف - ردود محلية فقط');

  console.log('\n🎯 اضغط Ctrl+C لإيقاف الخادم');
}).on('error', (error) => {
  console.error('❌ فشل في بدء الخادم:', error);
  if (error.code === 'EADDRINUSE') {
    console.log(`🔄 المنفذ ${PORT} مُستخدم. جرب منفذ آخر أو أوقف العملية الأخرى.`);
  }
  process.exit(1);
});

// معالجة إيقاف الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 تم إيقاف الخادم النظيف');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 تم إيقاف الخادم النظيف');
  process.exit(0);
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error);
  systemStats.errors++;
  console.log('🔄 النظام يحاول الاستمرار...');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promise مرفوض:', reason);
  console.log('📍 في:', promise);
  systemStats.errors++;
});

// تسجيل حالة النظام كل 5 دقائق
setInterval(() => {
  const memUsage = process.memoryUsage();
  const uptime = Math.floor((Date.now() - systemStats.startTime) / 1000);

  if (NODE_ENV === 'development') {
    console.log(`📊 حالة النظام:`);
    console.log(`   • وقت التشغيل: ${uptime} ثانية`);
    console.log(`   • استخدام الذاكرة: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
    console.log(`   • إجمالي الطلبات: ${systemStats.totalRequests}`);
    console.log(`   • الاتصالات النشطة: ${systemStats.activeConnections}`);
    console.log(`   • إجمالي الرسائل: ${systemStats.totalMessages}`);
    console.log(`   • الأخطاء: ${systemStats.errors}`);
  }
}, 300000);

console.log('🚀 تم تحميل الخادم النظيف والمحسن بنجاح!');
