# 🔗 دليل إعداد Webhook للتحديثات المباشرة

## 🎯 ما هو Webhook؟

**Webhook** هو آلية تسمح لسلة بإرسال إشعارات فورية لنظامك عند حدوث أحداث مهمة مثل:
- 📦 طلب جديد
- 🔄 تحديث حالة طلب  
- 🛍️ إضافة منتج جديد
- 📊 تغيير في المخزون
- 👤 تسجيل عميل جديد

---

## 🛠️ طرق إعداد URL عام

### 1. **استخدام ngrok (للاختبار السريع)**

#### **أ) تحميل وتثبيت ngrok:**
```bash
# تحميل من الموقع الرسمي
https://ngrok.com/download

# أو باستخدام chocolatey (Windows)
choco install ngrok

# أو باستخدام brew (Mac)
brew install ngrok
```

#### **ب) تشغيل النظام:**
```bash
# تشغيل النظام
npm start
# أو
node server-clean.js
```

#### **ج) إنشاء tunnel عام:**
```bash
# في terminal جديد
ngrok http 3001
```

#### **د) نسخ الرابط:**
```
Session Status                online
Account                       your-account (Plan: Free)
Version                       3.0.0
Region                        United States (us)
Latency                       45ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:3001
```

**رابط Webhook النهائي:** `https://abc123.ngrok.io/webhooks/salla`

---

### 2. **استخدام خادم حقيقي (للإنتاج)**

#### **أ) خدمات الاستضافة المقترحة:**
- **Heroku** - سهل ومجاني للبداية
- **DigitalOcean** - خوادم VPS قوية
- **AWS EC2** - مرونة عالية
- **Google Cloud** - أداء ممتاز

#### **ب) إعداد Domain:**
```
https://yourdomain.com/webhooks/salla
```

---

## 🔧 تسجيل Webhook في سلة

### **الطريقة الأولى: استخدام معالج الإعداد**

```bash
# تشغيل معالج الإعداد
node webhook-setup.js
```

### **الطريقة الثانية: يدوياً عبر API**

#### **أ) الحصول على Access Token:**
```javascript
const response = await axios.post('https://accounts.salla.sa/oauth2/token', {
  grant_type: 'client_credentials',
  client_id: 'your_client_id',
  client_secret: 'your_client_secret',
  scope: 'read write'
});
```

#### **ب) تسجيل Webhook:**
```javascript
const webhookData = {
  name: 'AI Chatbot Integration',
  url: 'https://abc123.ngrok.io/webhooks/salla',
  events: [
    'order.created',
    'order.updated', 
    'order.cancelled',
    'product.created',
    'product.updated',
    'customer.created'
  ],
  secret: 'your_webhook_secret'
};

await axios.post('https://api.salla.dev/admin/v2/webhooks', webhookData, {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  }
});
```

---

## 📋 الأحداث المدعومة

| الحدث | الوصف | البيانات المرسلة |
|-------|--------|------------------|
| `order.created` | طلب جديد | معلومات الطلب كاملة |
| `order.updated` | تحديث طلب | حالة الطلب الجديدة |
| `order.cancelled` | إلغاء طلب | سبب الإلغاء |
| `product.created` | منتج جديد | بيانات المنتج |
| `product.updated` | تحديث منتج | التغييرات الجديدة |
| `product.deleted` | حذف منتج | معرف المنتج |
| `customer.created` | عميل جديد | معلومات العميل |
| `customer.updated` | تحديث عميل | البيانات المحدثة |

---

## 🔒 الأمان والتحقق

### **التحقق من التوقيع:**
```javascript
function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}
```

### **إعدادات الأمان في .env:**
```env
WEBHOOK_SECRET=your_super_secret_webhook_key
WEBHOOK_URL=https://yourdomain.com/webhooks/salla
```

---

## 🧪 اختبار Webhook

### **1. تشغيل النظام:**
```bash
npm start
```

### **2. تشغيل ngrok:**
```bash
ngrok http 3001
```

### **3. تسجيل Webhook:**
```bash
node webhook-setup.js
```

### **4. اختبار من سلة:**
- قم بإنشاء طلب تجريبي في متجرك
- راقب logs النظام
- تحقق من قاعدة البيانات

### **5. مراقبة Logs:**
```bash
# عرض logs مباشرة
tail -f logs/combined.log

# أو من خلال واجهة الإدارة
http://localhost:3001/admin
```

---

## 📊 مراقبة الأداء

### **عرض سجل Webhooks:**
```javascript
// GET /webhooks/logs/:storeId
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1,
        "store_id": "123456",
        "event_type": "order.created",
        "status": "processed",
        "created_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "total": 50
    }
  }
}
```

### **إعادة معالجة Webhook فاشل:**
```javascript
// POST /webhooks/retry/:logId
{
  "success": true,
  "message": "تمت إعادة معالجة الحدث بنجاح"
}
```

---

## ⚠️ نصائح مهمة

### **للاختبار:**
- ✅ استخدم ngrok للاختبار السريع
- ✅ تأكد من تشغيل النظام قبل تسجيل Webhook
- ✅ راقب logs للتأكد من وصول البيانات

### **للإنتاج:**
- ✅ استخدم خادم حقيقي مع SSL
- ✅ احتفظ بنسخة احتياطية من WEBHOOK_SECRET
- ✅ راقب الأداء والأخطاء باستمرار

### **الأمان:**
- ✅ استخدم HTTPS دائماً
- ✅ تحقق من التوقيع في كل طلب
- ✅ سجل جميع الأحداث للمراجعة

---

## 🆘 حل المشاكل الشائعة

### **المشكلة: Webhook لا يصل**
```bash
# تحقق من:
1. هل النظام يعمل؟
2. هل ngrok يعمل؟
3. هل URL صحيح؟
4. هل تم تسجيل Webhook في سلة؟
```

### **المشكلة: خطأ في التوقيع**
```bash
# تحقق من:
1. WEBHOOK_SECRET في .env
2. نفس السر المستخدم في سلة
3. تنسيق التوقيع صحيح
```

### **المشكلة: بيانات غير مكتملة**
```bash
# تحقق من:
1. نوع الحدث مدعوم
2. صيغة JSON صحيحة
3. جميع الحقول المطلوبة موجودة
```

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع logs النظام
2. تحقق من إعدادات سلة
3. اختبر مع ngrok أولاً
4. راجع وثائق سلة الرسمية

**وثائق سلة:** https://docs.salla.dev/
