// ===================================================================
// أداة عرض محتويات قاعدة البيانات
// ===================================================================

const database = require('./backend/config/database');
require('dotenv').config();

async function viewDatabase() {
  try {
    console.log('🗄️ عرض محتويات قاعدة البيانات');
    console.log('=====================================\n');

    // تهيئة قاعدة البيانات
    await database.initialize();

    // عرض معلومات الجداول
    const tables = [
      'stores',
      'conversations', 
      'messages',
      'products_cache',
      'bot_settings',
      'analytics',
      'webhook_logs',
      'admin_users'
    ];

    for (const table of tables) {
      await showTableInfo(table);
    }

    console.log('\n✅ تم عرض جميع الجداول بنجاح');

  } catch (error) {
    console.error('❌ خطأ في عرض قاعدة البيانات:', error);
  }
}

async function showTableInfo(tableName) {
  try {
    console.log(`\n📊 جدول: ${tableName}`);
    console.log('─'.repeat(50));

    // عدد السجلات
    const count = await database.get(`SELECT COUNT(*) as count FROM ${tableName}`);
    console.log(`📈 عدد السجلات: ${count.count}`);

    // عرض بعض السجلات إذا وجدت
    if (count.count > 0) {
      const records = await database.all(`SELECT * FROM ${tableName} LIMIT 3`);
      
      console.log('\n📋 عينة من البيانات:');
      records.forEach((record, index) => {
        console.log(`\n${index + 1}. السجل رقم ${record.id || 'N/A'}:`);
        
        // عرض الحقول المهمة حسب نوع الجدول
        switch (tableName) {
          case 'stores':
            console.log(`   • اسم المتجر: ${record.store_name || 'غير محدد'}`);
            console.log(`   • رابط المتجر: ${record.store_url || 'غير محدد'}`);
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • نشط: ${record.is_active ? 'نعم' : 'لا'}`);
            break;
            
          case 'conversations':
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • اسم العميل: ${record.customer_name || 'غير محدد'}`);
            console.log(`   • معرف الجلسة: ${record.session_id || 'غير محدد'}`);
            console.log(`   • الحالة: ${record.status || 'غير محدد'}`);
            break;
            
          case 'messages':
            console.log(`   • معرف المحادثة: ${record.conversation_id || 'غير محدد'}`);
            console.log(`   • نوع المرسل: ${record.sender_type || 'غير محدد'}`);
            console.log(`   • الرسالة: ${record.message_text?.substring(0, 50) || 'غير محدد'}...`);
            console.log(`   • نوع الرسالة: ${record.message_type || 'غير محدد'}`);
            break;
            
          case 'products_cache':
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • معرف المنتج: ${record.product_id || 'غير محدد'}`);
            console.log(`   • آخر تحديث: ${record.last_updated || 'غير محدد'}`);
            break;
            
          case 'bot_settings':
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • مفتاح الإعداد: ${record.setting_key || 'غير محدد'}`);
            console.log(`   • قيمة الإعداد: ${record.setting_value?.substring(0, 30) || 'غير محدد'}...`);
            break;
            
          case 'analytics':
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • نوع الحدث: ${record.event_type || 'غير محدد'}`);
            console.log(`   • معرف الجلسة: ${record.user_session || 'غير محدد'}`);
            break;
            
          case 'webhook_logs':
            console.log(`   • معرف المتجر: ${record.store_id || 'غير محدد'}`);
            console.log(`   • نوع الحدث: ${record.event_type || 'غير محدد'}`);
            console.log(`   • الحالة: ${record.status || 'غير محدد'}`);
            console.log(`   • عدد المحاولات: ${record.attempts || 0}`);
            break;
            
          case 'admin_users':
            console.log(`   • اسم المستخدم: ${record.username || 'غير محدد'}`);
            console.log(`   • البريد الإلكتروني: ${record.email || 'غير محدد'}`);
            console.log(`   • الاسم الكامل: ${record.full_name || 'غير محدد'}`);
            console.log(`   • الدور: ${record.role || 'غير محدد'}`);
            console.log(`   • نشط: ${record.is_active ? 'نعم' : 'لا'}`);
            break;
            
          default:
            // عرض أول 3 حقول
            const keys = Object.keys(record).slice(0, 3);
            keys.forEach(key => {
              console.log(`   • ${key}: ${record[key] || 'غير محدد'}`);
            });
        }
        
        console.log(`   • تاريخ الإنشاء: ${record.created_at || 'غير محدد'}`);
      });
      
      if (count.count > 3) {
        console.log(`\n... و ${count.count - 3} سجل آخر`);
      }
    } else {
      console.log('📭 الجدول فارغ');
    }
  } catch (error) {
    console.error(`❌ خطأ في عرض جدول ${tableName}:`, error.message);
  }
}

// دالة لعرض إحصائيات سريعة
async function showQuickStats() {
  try {
    console.log('\n📊 إحصائيات سريعة');
    console.log('==================');

    const stats = await Promise.all([
      database.get('SELECT COUNT(*) as count FROM stores WHERE is_active = 1'),
      database.get('SELECT COUNT(*) as count FROM conversations'),
      database.get('SELECT COUNT(*) as count FROM messages'),
      database.get('SELECT COUNT(*) as count FROM products_cache'),
      database.get('SELECT COUNT(*) as count FROM webhook_logs'),
      database.get('SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1')
    ]);

    console.log(`🏪 المتاجر النشطة: ${stats[0].count}`);
    console.log(`💬 إجمالي المحادثات: ${stats[1].count}`);
    console.log(`📝 إجمالي الرسائل: ${stats[2].count}`);
    console.log(`🛍️ المنتجات المخزنة: ${stats[3].count}`);
    console.log(`🔗 سجلات Webhook: ${stats[4].count}`);
    console.log(`👨‍💼 المستخدمين الإداريين: ${stats[5].count}`);

    // إحصائيات اليوم
    const todayStats = await Promise.all([
      database.get("SELECT COUNT(*) as count FROM conversations WHERE DATE(created_at) = DATE('now')"),
      database.get("SELECT COUNT(*) as count FROM messages WHERE DATE(created_at) = DATE('now')"),
      database.get("SELECT COUNT(*) as count FROM webhook_logs WHERE DATE(created_at) = DATE('now')")
    ]);

    console.log('\n📅 إحصائيات اليوم:');
    console.log(`💬 محادثات جديدة: ${todayStats[0].count}`);
    console.log(`📝 رسائل جديدة: ${todayStats[1].count}`);
    console.log(`🔗 أحداث Webhook: ${todayStats[2].count}`);

  } catch (error) {
    console.error('❌ خطأ في عرض الإحصائيات:', error);
  }
}

// دالة لإضافة بيانات تجريبية
async function addSampleData() {
  try {
    console.log('\n🧪 إضافة بيانات تجريبية');
    console.log('========================');

    // إضافة متجر تجريبي
    await database.run(
      `INSERT OR IGNORE INTO stores (store_id, store_name, store_url, is_active)
       VALUES (?, ?, ?, ?)`,
      ['demo_store_001', 'متجر تجريبي للاختبار', 'https://demo-store.salla.sa', 1]
    );

    // إضافة محادثة تجريبية
    const conversation = await database.run(
      `INSERT INTO conversations (store_id, session_id, customer_name, status)
       VALUES (?, ?, ?, ?)`,
      ['demo_store_001', 'session_' + Date.now(), 'عميل تجريبي', 'active']
    );

    // إضافة رسائل تجريبية
    await database.run(
      `INSERT INTO messages (conversation_id, sender_type, message_text, message_type)
       VALUES (?, ?, ?, ?)`,
      [conversation.id, 'customer', 'مرحباً، أريد المساعدة في اختيار منتج', 'text']
    );

    await database.run(
      `INSERT INTO messages (conversation_id, sender_type, message_text, message_type)
       VALUES (?, ?, ?, ?)`,
      [conversation.id, 'bot', 'مرحباً بك! سأكون سعيداً لمساعدتك. ما نوع المنتج الذي تبحث عنه؟', 'text']
    );

    console.log('✅ تم إضافة البيانات التجريبية بنجاح');

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
  }
}

// تشغيل البرنامج
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--stats')) {
    await database.initialize();
    await showQuickStats();
  } else if (args.includes('--sample')) {
    await database.initialize();
    await addSampleData();
    await showQuickStats();
  } else {
    await viewDatabase();
    await showQuickStats();
  }
  
  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { viewDatabase, showQuickStats, addSampleData };
