const express = require('express');
const router = express.Router();
const database = require('../config/database');
const logger = require('../config/logger');
const { authMiddleware } = require('../middleware/auth');

// تطبيق middleware المصادقة على جميع المسارات (مؤقتاً معطل للاختبار)
// router.use(authMiddleware);

// لوحة المعلومات الرئيسية
router.get('/dashboard', async (req, res) => {
  try {
    // إحصائيات عامة
    const stats = await Promise.all([
      // عدد المتاجر
      database.get('SELECT COUNT(*) as count FROM stores WHERE is_active = 1'),

      // عدد المحادثات اليوم
      database.get(`
        SELECT COUNT(*) as count FROM conversations
        WHERE DATE(created_at) = DATE('now')
      `),

      // عدد الرسائل اليوم
      database.get(`
        SELECT COUNT(*) as count FROM messages
        WHERE DATE(created_at) = DATE('now')
      `),

      // عدد المنتجات في الكاش
      database.get('SELECT COUNT(*) as count FROM products_cache')
    ]);

    // إحصائيات المحادثات الأسبوعية
    const weeklyConversations = await database.all(`
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM conversations
      WHERE created_at >= DATE('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    // أحدث المحادثات
    const recentConversations = await database.all(`
      SELECT c.*, s.store_name
      FROM conversations c
      LEFT JOIN stores s ON c.store_id = s.store_id
      ORDER BY c.created_at DESC
      LIMIT 10
    `);

    // المتاجر النشطة
    const activeStores = await database.all(`
      SELECT store_id, store_name, store_url, created_at
      FROM stores
      WHERE is_active = 1
      ORDER BY created_at DESC
    `);

    res.json({
      success: true,
      data: {
        stats: {
          activeStores: stats[0].count,
          todayConversations: stats[1].count,
          todayMessages: stats[2].count,
          cachedProducts: stats[3].count
        },
        charts: {
          weeklyConversations: weeklyConversations
        },
        recentConversations: recentConversations,
        activeStores: activeStores
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب بيانات لوحة المعلومات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب البيانات'
    });
  }
});

// إحصائيات المحادثات
router.get('/analytics/conversations', async (req, res) => {
  try {
    const { storeId, period = '7d' } = req.query;

    let dateFilter = '';
    switch (period) {
      case '24h':
        dateFilter = "created_at >= DATE('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "created_at >= DATE('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "created_at >= DATE('now', '-30 days')";
        break;
      default:
        dateFilter = "created_at >= DATE('now', '-7 days')";
    }

    let storeFilter = '';
    let params = [];
    if (storeId) {
      storeFilter = 'AND store_id = ?';
      params.push(storeId);
    }

    // إحصائيات المحادثات
    const conversationStats = await database.all(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total_conversations,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_conversations,
        COUNT(CASE WHEN status = 'ended' THEN 1 END) as ended_conversations
      FROM conversations
      WHERE ${dateFilter} ${storeFilter}
      GROUP BY DATE(created_at)
      ORDER BY date
    `, params);

    // إحصائيات الرسائل
    const messageStats = await database.all(`
      SELECT
        DATE(m.created_at) as date,
        COUNT(*) as total_messages,
        COUNT(CASE WHEN m.sender_type = 'customer' THEN 1 END) as customer_messages,
        COUNT(CASE WHEN m.sender_type = 'bot' THEN 1 END) as bot_messages
      FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE ${dateFilter.replace('created_at', 'm.created_at')} ${storeFilter.replace('store_id', 'c.store_id')}
      GROUP BY DATE(m.created_at)
      ORDER BY date
    `, params);

    res.json({
      success: true,
      data: {
        conversations: conversationStats,
        messages: messageStats,
        period: period
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب إحصائيات المحادثات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات'
    });
  }
});

// إدارة المحادثات
router.get('/conversations', async (req, res) => {
  try {
    const { page = 1, limit = 20, storeId, status } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    let params = [];

    if (storeId) {
      whereClause += ' AND c.store_id = ?';
      params.push(storeId);
    }

    if (status) {
      whereClause += ' AND c.status = ?';
      params.push(status);
    }

    const conversations = await database.all(`
      SELECT
        c.*,
        s.store_name,
        COUNT(m.id) as message_count,
        MAX(m.created_at) as last_message_at
      FROM conversations c
      LEFT JOIN stores s ON c.store_id = s.store_id
      LEFT JOIN messages m ON c.id = m.conversation_id
      ${whereClause}
      GROUP BY c.id
      ORDER BY c.updated_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    const total = await database.get(`
      SELECT COUNT(*) as count
      FROM conversations c
      ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        conversations: conversations,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total.count,
          pages: Math.ceil(total.count / limit)
        }
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب المحادثات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المحادثات'
    });
  }
});

// تفاصيل محادثة
router.get('/conversations/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const conversation = await database.get(`
      SELECT c.*, s.store_name, s.store_url
      FROM conversations c
      LEFT JOIN stores s ON c.store_id = s.store_id
      WHERE c.id = ?
    `, [conversationId]);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'المحادثة غير موجودة'
      });
    }

    const messages = await database.all(`
      SELECT * FROM messages
      WHERE conversation_id = ?
      ORDER BY created_at ASC
    `, [conversationId]);

    // تحويل البيانات الوصفية من JSON
    const formattedMessages = messages.map(msg => ({
      ...msg,
      metadata: msg.metadata ? JSON.parse(msg.metadata) : {}
    }));

    res.json({
      success: true,
      data: {
        conversation: conversation,
        messages: formattedMessages
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب تفاصيل المحادثة', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب تفاصيل المحادثة'
    });
  }
});

// إعدادات الروبوت
router.get('/bot-settings/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;

    const settings = await database.all(
      'SELECT setting_key, setting_value FROM bot_settings WHERE store_id = ?',
      [storeId]
    );

    // تحويل النتائج إلى كائن
    const settingsObject = {};
    settings.forEach(setting => {
      try {
        settingsObject[setting.setting_key] = JSON.parse(setting.setting_value);
      } catch {
        settingsObject[setting.setting_key] = setting.setting_value;
      }
    });

    res.json({
      success: true,
      data: settingsObject
    });

  } catch (error) {
    logger.logError('فشل في جلب إعدادات الروبوت', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإعدادات'
    });
  }
});

// تحديث إعدادات الروبوت
router.put('/bot-settings/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;
    const settings = req.body;

    // حفظ كل إعداد
    for (const [key, value] of Object.entries(settings)) {
      const settingValue = typeof value === 'object' ? JSON.stringify(value) : value;

      await database.run(`
        INSERT OR REPLACE INTO bot_settings (store_id, setting_key, setting_value, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `, [storeId, key, settingValue]);
    }

    logger.logSuccess('تم تحديث إعدادات الروبوت', { storeId, settingsCount: Object.keys(settings).length });

    res.json({
      success: true,
      message: 'تم تحديث الإعدادات بنجاح'
    });

  } catch (error) {
    logger.logError('فشل في تحديث إعدادات الروبوت', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث الإعدادات'
    });
  }
});

// تصدير البيانات
router.get('/export/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { storeId, startDate, endDate } = req.query;

    let data = [];
    let filename = '';

    switch (type) {
      case 'conversations':
        data = await database.all(`
          SELECT c.*, s.store_name
          FROM conversations c
          LEFT JOIN stores s ON c.store_id = s.store_id
          WHERE (? IS NULL OR c.store_id = ?)
          AND (? IS NULL OR c.created_at >= ?)
          AND (? IS NULL OR c.created_at <= ?)
          ORDER BY c.created_at DESC
        `, [storeId, storeId, startDate, startDate, endDate, endDate]);
        filename = 'conversations.json';
        break;

      case 'messages':
        data = await database.all(`
          SELECT m.*, c.store_id, s.store_name
          FROM messages m
          JOIN conversations c ON m.conversation_id = c.id
          LEFT JOIN stores s ON c.store_id = s.store_id
          WHERE (? IS NULL OR c.store_id = ?)
          AND (? IS NULL OR m.created_at >= ?)
          AND (? IS NULL OR m.created_at <= ?)
          ORDER BY m.created_at DESC
        `, [storeId, storeId, startDate, startDate, endDate, endDate]);
        filename = 'messages.json';
        break;

      case 'analytics':
        data = await database.all(`
          SELECT *
          FROM analytics
          WHERE (? IS NULL OR store_id = ?)
          AND (? IS NULL OR created_at >= ?)
          AND (? IS NULL OR created_at <= ?)
          ORDER BY created_at DESC
        `, [storeId, storeId, startDate, startDate, endDate, endDate]);
        filename = 'analytics.json';
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'نوع التصدير غير مدعوم'
        });
    }

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.json(data);

  } catch (error) {
    logger.logError('فشل في تصدير البيانات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تصدير البيانات'
    });
  }
});

// حالة Webhook
router.get('/webhook-status', async (req, res) => {
  try {
    // جلب آخر webhook مستلم
    const lastWebhook = await database.get(`
      SELECT * FROM webhook_logs
      ORDER BY created_at DESC
      LIMIT 1
    `);

    // عدد webhooks اليوم
    const todayCount = await database.get(`
      SELECT COUNT(*) as count FROM webhook_logs
      WHERE DATE(created_at) = DATE('now')
    `);

    // عدد webhooks الناجحة
    const successCount = await database.get(`
      SELECT COUNT(*) as count FROM webhook_logs
      WHERE status = 'processed' AND DATE(created_at) = DATE('now')
    `);

    res.json({
      success: true,
      data: {
        isActive: lastWebhook ? true : false,
        lastUpdate: lastWebhook ? lastWebhook.created_at : null,
        eventsCount: todayCount.count,
        successRate: todayCount.count > 0 ? (successCount.count / todayCount.count * 100).toFixed(1) : 0,
        lastEvent: lastWebhook ? {
          type: lastWebhook.event_type,
          status: lastWebhook.status,
          time: lastWebhook.created_at
        } : null
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب حالة Webhook', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب حالة Webhook'
    });
  }
});

// اختبار Webhook
router.post('/test-webhook', async (req, res) => {
  try {
    // إنشاء حدث تجريبي
    const testEvent = {
      event: 'test.webhook',
      data: {
        id: 'test_' + Date.now(),
        message: 'هذا اختبار للـ Webhook',
        timestamp: new Date().toISOString()
      },
      merchant: {
        id: 'test_store',
        name: 'متجر تجريبي'
      }
    };

    // حفظ الحدث التجريبي
    await database.run(
      `INSERT INTO webhook_logs (store_id, event_type, payload, status, created_at)
       VALUES (?, ?, ?, 'test', CURRENT_TIMESTAMP)`,
      ['test_store', 'test.webhook', JSON.stringify(testEvent)]
    );

    logger.logSuccess('تم إرسال اختبار Webhook', testEvent);

    res.json({
      success: true,
      message: 'تم إرسال اختبار Webhook بنجاح',
      data: testEvent
    });

  } catch (error) {
    logger.logError('فشل في اختبار Webhook', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في اختبار Webhook'
    });
  }
});

// إحصائيات Webhook
router.get('/webhook-analytics', async (req, res) => {
  try {
    const { period = '7d' } = req.query;

    let dateFilter = '';
    switch (period) {
      case '24h':
        dateFilter = "created_at >= DATE('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "created_at >= DATE('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "created_at >= DATE('now', '-30 days')";
        break;
      default:
        dateFilter = "created_at >= DATE('now', '-7 days')";
    }

    // إحصائيات يومية
    const dailyStats = await database.all(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as total_events,
        COUNT(CASE WHEN status = 'processed' THEN 1 END) as successful_events,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_events
      FROM webhook_logs
      WHERE ${dateFilter}
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    // إحصائيات حسب نوع الحدث
    const eventTypeStats = await database.all(`
      SELECT
        event_type,
        COUNT(*) as count,
        COUNT(CASE WHEN status = 'processed' THEN 1 END) as successful
      FROM webhook_logs
      WHERE ${dateFilter}
      GROUP BY event_type
      ORDER BY count DESC
    `);

    // إحصائيات حسب المتجر
    const storeStats = await database.all(`
      SELECT
        w.store_id,
        s.store_name,
        COUNT(*) as total_events,
        COUNT(CASE WHEN w.status = 'processed' THEN 1 END) as successful_events
      FROM webhook_logs w
      LEFT JOIN stores s ON w.store_id = s.store_id
      WHERE ${dateFilter}
      GROUP BY w.store_id
      ORDER BY total_events DESC
    `);

    res.json({
      success: true,
      data: {
        dailyStats: dailyStats,
        eventTypeStats: eventTypeStats,
        storeStats: storeStats,
        period: period
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب إحصائيات Webhook', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات'
    });
  }
});

// إحصائيات قاعدة البيانات
router.get('/database-stats', async (req, res) => {
  try {
    const stats = await Promise.all([
      database.get('SELECT COUNT(*) as count FROM stores WHERE is_active = 1'),
      database.get('SELECT COUNT(*) as count FROM conversations'),
      database.get('SELECT COUNT(*) as count FROM messages'),
      database.get('SELECT COUNT(*) as count FROM products_cache'),
      database.get('SELECT COUNT(*) as count FROM bot_settings'),
      database.get('SELECT COUNT(*) as count FROM analytics'),
      database.get('SELECT COUNT(*) as count FROM webhook_logs'),
      database.get('SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1')
    ]);

    res.json({
      success: true,
      data: {
        stores: stats[0].count,
        conversations: stats[1].count,
        messages: stats[2].count,
        products: stats[3].count,
        settings: stats[4].count,
        analytics: stats[5].count,
        webhooks: stats[6].count,
        users: stats[7].count
      }
    });

  } catch (error) {
    logger.logError('فشل في جلب إحصائيات قاعدة البيانات', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات'
    });
  }
});

// بيانات جدول محدد
router.get('/table-data/:tableName', async (req, res) => {
  try {
    const { tableName } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    // التحقق من أن اسم الجدول آمن
    const allowedTables = [
      'stores', 'conversations', 'messages', 'products_cache',
      'bot_settings', 'analytics', 'webhook_logs', 'admin_users'
    ];

    if (!allowedTables.includes(tableName)) {
      return res.status(400).json({
        success: false,
        message: 'اسم الجدول غير صحيح'
      });
    }

    const data = await database.all(
      `SELECT * FROM ${tableName} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
      [parseInt(limit), parseInt(offset)]
    );

    res.json({
      success: true,
      data: data
    });

  } catch (error) {
    logger.logError('فشل في جلب بيانات الجدول', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب البيانات'
    });
  }
});

module.exports = router;
