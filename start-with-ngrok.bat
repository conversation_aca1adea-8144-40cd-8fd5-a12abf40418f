@echo off
title 🔗 تشغيل النظام مع ngrok للتحديثات المباشرة

echo.
echo ==========================================
echo 🚀 تشغيل نظام تكامل الذكاء الاصطناعي مع سلة
echo 🔗 مع إعداد Webhook للتحديثات المباشرة
echo ==========================================
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت. يرجى تثبيته من: https://nodejs.org
    pause
    exit /b 1
)

:: التحقق من وجود ngrok
ngrok version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ ngrok غير مثبت
    echo.
    echo 📥 يرجى تحميل ngrok من: https://ngrok.com/download
    echo أو تثبيته باستخدام: choco install ngrok
    echo.
    echo بعد التثبيت، شغل هذا الملف مرة أخرى
    pause
    exit /b 1
)

echo ✅ Node.js و ngrok متوفران
echo.

:: تشغيل النظام في الخلفية
echo 🚀 تشغيل النظام...
start "Salla AI System" cmd /k "node server-clean.js"

:: انتظار قليل لتشغيل النظام
timeout /t 3 /nobreak >nul

:: تشغيل ngrok
echo 🌐 تشغيل ngrok لإنشاء رابط عام...
echo.
echo 📋 ملاحظات مهمة:
echo • سيتم إنشاء رابط عام للنظام
echo • انسخ الرابط الذي يبدأ بـ https://
echo • أضف /webhooks/salla في النهاية
echo • استخدم الرابط الكامل في إعدادات سلة
echo.
echo مثال: https://abc123.ngrok.io/webhooks/salla
echo.

pause

:: تشغيل ngrok
ngrok http 3001

echo.
echo 🎉 تم إيقاف ngrok
echo 🔄 لإعادة التشغيل، شغل هذا الملف مرة أخرى
pause
